# Camera connection issues
1. Check if camera is visible with arena sdk `Utilities/IpConfigUtility /force`. If not, try:
   - Turn off and on NIC
   - Reboot PC
   - Reboot (reconnect) camera
2. Run `infra/set_camera_ip.sh`.

# Exception: arena_api requires 'pip' to be available at runtime to get arena_api.__version__ value.
Make sure to run the script in a fully fledged pipenv.  
For example: `pipenv run python capture_and_save.py`.  
In VS Code: run `pipenv shell` in the visual studio code Python Debug Console and try again.
